<?php
/*
Plugin Name: Google Site Translator
Description: Tüm siteyi Google Translate ile otomatik olarak çevirir.
Version: 1.0
Author: <PERSON>
*/

if (!defined('ABSPATH')) exit;

// Admin panelde ana menü ve ayar sayfası ekle
function gst_add_admin_menu() {
    add_menu_page(
        'Google Site Translator',
        'Site Translator',
        'manage_options',
        'google-site-translator',
        'gst_settings_page',
        'dashicons-translation',
        81
    );
    add_submenu_page(
        'google-site-translator',
        'Ayarlar',
        'Ayarlar',
        'manage_options',
        'google-site-translator',
        'gst_settings_page'
    );
}
add_action('admin_menu', 'gst_add_admin_menu');

// Google Translate destekli diller ve ülke kodları
function gst_get_all_languages() {
    return [
        'ar' => ['Arabic', 'sa'],
        'az' => ['Azerbaijani', 'az'],
        'be' => ['Belarusian', 'by'],
        'bg' => ['Bulgarian', 'bg'],
        'zh' => ['Chinese', 'cn'],
        'hr' => ['Croatian', 'hr'],
        'cs' => ['Czech', 'cz'],
        'da' => ['Danish', 'dk'],
        'nl' => ['Dutch', 'nl'],
        'en' => ['English', 'gb'],
        'et' => ['Estonian', 'ee'],
        'fi' => ['Finnish', 'fi'],
        'fr' => ['French', 'fr'],
        'de' => ['German', 'de'],
        'el' => ['Greek', 'gr'],
        'he' => ['Hebrew', 'il'],
        'hi' => ['Hindi', 'in'],
        'hu' => ['Hungarian', 'hu'],
        'id' => ['Indonesian', 'id'],
        'it' => ['Italian', 'it'],
        'ja' => ['Japanese', 'jp'],
        'kk' => ['Kazakh', 'kz'],
        'ko' => ['Korean', 'kr'],
        'lv' => ['Latvian', 'lv'],
        'lt' => ['Lithuanian', 'lt'],
        'mk' => ['Macedonian', 'mk'],
        'ms' => ['Malay', 'my'],
        'no' => ['Norwegian', 'no'],
        'fa' => ['Persian', 'ir'],
        'pl' => ['Polish', 'pl'],
        'pt' => ['Portuguese', 'pt'],
        'ro' => ['Romanian', 'ro'],
        'ru' => ['Russian', 'ru'],
        'sr' => ['Serbian', 'rs'],
        'sk' => ['Slovak', 'sk'],
        'sl' => ['Slovenian', 'si'],
        'es' => ['Spanish', 'es'],
        'sv' => ['Swedish', 'se'],
        'th' => ['Thai', 'th'],
        'tr' => ['Turkish', 'tr'],
        'uk' => ['Ukrainian', 'ua'],
        'uz' => ['Uzbek', 'uz'],
        'vi' => ['Vietnamese', 'vn']
    ];
}

// Admin panelde ayar sayfası
function gst_settings_page() {
    $all_languages = gst_get_all_languages();
    $selected = get_option('gst_languages', ['en','de','fr','ru','ar','zh','es','it']);
    $widget_location = get_option('gst_widget_location', 'footer');
    $menu_locations = get_nav_menu_locations();
    $menus = wp_get_nav_menus();
    $selected_menu = get_option('gst_selected_menu', '');
    $locations = [
        'footer' => 'Footer',
        'top' => 'Top Menü',
        'main' => 'Main Menü',
        'custom_menu' => 'WordPress Menüleri'
    ];
    ?>
    <div class="gst-settings-wrap">
        <h2>Google Site Translator Ayarları</h2>
        <form method="post" id="gst-settings-form">
            <div style="margin-bottom:15px;">
                <label style="font-weight:600;">
                    <input type="checkbox" id="gst-select-all" />
                    Tümünü Seç / Seçme
                </label>
            </div>
            <div class="gst-lang-list">
                <?php foreach ($all_languages as $code => $info): 
                    $lang_name = $info[0];
                    $country_code = $info[1];
                    ?>
                    <label class="gst-lang-item">
                        <input type="checkbox" class="gst-lang-checkbox" name="gst_languages[]" value="<?php echo esc_attr($code); ?>" <?php checked(in_array($code, $selected)); ?>>
                        <img src="https://flagcdn.com/24x18/<?php echo esc_attr($country_code); ?>.png" alt="<?php echo esc_attr($lang_name); ?>" style="width:24px;height:18px;margin-right:7px;border-radius:3px;box-shadow:0 1px 2px rgba(0,0,0,0.07);vertical-align:middle;">
                        <span><?php echo esc_html($lang_name); ?></span>
                    </label>
                <?php endforeach; ?>
            </div>
            <div class="gst-location-list" style="margin-bottom:20px;">
                <label style="font-weight:600;display:block;margin-bottom:8px;">Widget Konumu:</label>
                <?php foreach ($locations as $key => $label): ?>
                    <label class="gst-location-item" style="margin-right:20px;">
                        <input type="radio" name="gst_widget_location" value="<?php echo esc_attr($key); ?>" <?php checked($widget_location, $key); ?> onchange="document.getElementById('gst-menu-select-wrap').style.display = (this.value === 'custom_menu') ? 'block' : 'none';">
                        <span><?php echo esc_html($label); ?></span>
                    </label>
                <?php endforeach; ?>
            </div>
            <div id="gst-menu-select-wrap" style="margin-bottom:20px;<?php echo ($widget_location === 'custom_menu') ? '' : 'display:none;'; ?>">
                <label style="font-weight:600;display:block;margin-bottom:8px;">WordPress Menüleri:</label>
                <select name="gst_selected_menu" style="min-width:200px;">
                    <option value="">Menü Seçiniz</option>
                    <?php foreach ($menus as $menu): ?>
                        <option value="<?php echo esc_attr($menu->term_id); ?>" <?php selected($selected_menu, $menu->term_id); ?>>
                            <?php echo esc_html($menu->name); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <button type="submit" name="gst_save_settings" class="button button-primary">Kaydet</button>
        </form>
    </div>
    <style>
        .gst-settings-wrap { max-width:700px; margin:30px auto; background:#fff; padding:30px; border-radius:10px; box-shadow:0 2px 10px rgba(0,0,0,0.07);}
        .gst-lang-list { display:flex; flex-wrap:wrap; gap:15px; margin-bottom:20px;}
        .gst-lang-item { display:flex; align-items:center; background:#f7f7f7; padding:10px 15px; border-radius:8px; box-shadow:0 1px 4px rgba(0,0,0,0.04);}
        .gst-lang-item input[type="checkbox"] { margin-right:8px; accent-color:#007cba; width:18px; height:18px;}
        .gst-location-list { display:flex; flex-wrap:wrap; gap:10px;}
        .gst-location-item { display:flex; align-items:center;}
        .gst-location-item input[type="radio"] { margin-right:6px; accent-color:#007cba;}
        @media(max-width:900px){ .gst-lang-list{gap:8px;} .gst-settings-wrap{padding:15px;} }
        @media(max-width:600px){ .gst-lang-list{flex-direction:column;gap:10px;} .gst-settings-wrap{padding:10px;} .gst-location-list{flex-direction:column;gap:8px;} }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var selectAll = document.getElementById('gst-select-all');
            var checkboxes = document.querySelectorAll('.gst-lang-checkbox');
            selectAll.addEventListener('change', function() {
                checkboxes.forEach(function(cb) {
                    cb.checked = selectAll.checked;
                });
            });
            // Menü selectbox'ı göster/gizle
            var radios = document.querySelectorAll('input[name="gst_widget_location"]');
            var menuWrap = document.getElementById('gst-menu-select-wrap');
            radios.forEach(function(radio){
                radio.addEventListener('change', function(){
                    menuWrap.style.display = (this.value === 'custom_menu') ? 'block' : 'none';
                });
            });
            var observer = new MutationObserver(function() {
              var select = document.querySelector('#google_translate_element select.goog-te-combo');
              if (select && !select.dataset.reloadAdded) {
                select.addEventListener('change', function() {
                  location.reload();
                });
                select.dataset.reloadAdded = '1';
              }
            });
            observer.observe(document.body, { childList: true, subtree: true });
        });
    </script>
    <?php
}

// Ayarları kaydet
function gst_save_settings() {
    if (isset($_POST['gst_languages'])) {
        $langs = array_map('sanitize_text_field', $_POST['gst_languages']);
        update_option('gst_languages', $langs);
    }
    if (isset($_POST['gst_widget_location'])) {
        update_option('gst_widget_location', sanitize_text_field($_POST['gst_widget_location']));
    }
    if (isset($_POST['gst_selected_menu'])) {
        update_option('gst_selected_menu', sanitize_text_field($_POST['gst_selected_menu']));
    }
}
if (is_admin() && isset($_POST['gst_save_settings'])) {
    gst_save_settings();
}

// Widget'ı frontend'de göster
function gst_render_translate_widget($menu_mode = false) {
    $selected = get_option('gst_languages', ['en','de','fr','ru','ar','zh','es','it']);
    if (empty($selected) || !in_array('en', $selected)) {
        array_unshift($selected, 'en');
    }
    $included = implode(',', $selected);

    $default_code = 'en';
    $all_languages = gst_get_all_languages();
    $default_flag = isset($all_languages[$default_code]) ? $all_languages[$default_code][1] : 'gb';

    if ($menu_mode) {
        $switcher_id = 'gst-switcher-' . uniqid();
?>
<div class="gst-switcher-custom-menu-item" id="<?php echo $switcher_id; ?>" style="position:relative;max-width:120px;">
  <a href="#" class="gst-switcher-selected" style="display:flex;align-items:center;justify-content:space-between;padding:6px 10px;background:none;border:none;box-shadow:none;cursor:pointer;outline:none;min-width:90px;max-width:120px;text-decoration:none;color:inherit;">
    <span style="display:flex;align-items:center;">
      <img id="<?php echo $switcher_id; ?>-flag" src="https://flagcdn.com/24x18/<?php echo esc_attr($default_flag); ?>.png" style="width:18px;height:14px;margin-right:6px;border-radius:3px;box-shadow:0 1px 2px rgba(0,0,0,0.09);">
      <span id="<?php echo $switcher_id; ?>-lang" style="font-size:14px;font-weight:500;color:#222;white-space:nowrap;max-width:70px;overflow:hidden;text-overflow:ellipsis;display:inline-block;"><?php echo esc_html($all_languages[$default_code][0]); ?></span>
    </span>
    <span style="font-size:15px;color:#888;">&#9662;</span>
  </a>
  <div class="gst-switcher-dropdown" style="display:none;position:absolute;top:110%;left:0;width:100%;background:#fff;border-radius:7px;box-shadow:0 4px 16px rgba(0,0,0,0.11);z-index:999;max-height:180px;overflow:auto;padding:4px 0;min-width:90px;max-width:120px;">
    <?php foreach ($selected as $code):
      $lang_name = isset($all_languages[$code]) ? $all_languages[$code][0] : $code;
      $flag_code = isset($all_languages[$code]) ? $all_languages[$code][1] : 'gb';
    ?>
    <div class="gst-switcher-option" data-code="<?php echo esc_attr($code); ?>" data-flag="<?php echo esc_attr($flag_code); ?>" style="display:flex;align-items:center;padding:6px 10px;cursor:pointer;transition:background .2s;font-size:13px;max-width:120px;">
      <img src="https://flagcdn.com/24x18/<?php echo esc_attr($flag_code); ?>.png" style="width:18px;height:14px;margin-right:6px;border-radius:3px;box-shadow:0 1px 2px rgba(0,0,0,0.09);">
      <span style="color:#222;white-space:nowrap;max-width:70px;overflow:hidden;text-overflow:ellipsis;display:inline-block;"><?php echo esc_html($lang_name); ?></span>
    </div>
    <?php endforeach; ?>
  </div>
  <div id="google_translate_element_menu" style="display:none;"></div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function(){
  var root = document.getElementById('<?php echo $switcher_id; ?>');
  var selectedBtn = root.querySelector('.gst-switcher-selected');
  var dropdown = root.querySelector('.gst-switcher-dropdown');
  var flagImg = root.querySelector('#<?php echo $switcher_id; ?>-flag');
  var langSpan = root.querySelector('#<?php echo $switcher_id; ?>-lang');
  selectedBtn.addEventListener('click', function(e){
    e.preventDefault();
    dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
  });
  dropdown.querySelectorAll('.gst-switcher-option').forEach(function(opt){
    opt.addEventListener('click', function(){
      var code = this.getAttribute('data-code');
      var flag = this.getAttribute('data-flag');
      flagImg.src = 'https://flagcdn.com/24x18/' + flag + '.png';
      langSpan.textContent = this.textContent.trim();
      dropdown.style.display = 'none';
      var tryCount = 0;
      function setGoogleTranslate(){
        var googleSelect = document.querySelector('#google_translate_element select.goog-te-combo');
        if(!googleSelect) {
          // iframe içinde arama
          var iframe = document.querySelector('#google_translate_element iframe');
          if(iframe) {
            try {
              var innerDoc = iframe.contentDocument || iframe.contentWindow.document;
              googleSelect = innerDoc.querySelector('select.goog-te-combo');
              if(googleSelect) {
                googleSelect.value = code;
                googleSelect.dispatchEvent(new Event('change'));
                return;
              }
            } catch(e) {}
          }
        }
        if(googleSelect){
          googleSelect.value = code;
          googleSelect.dispatchEvent(new Event('change'));
        } else if(tryCount < 20) {
          tryCount++;
          setTimeout(setGoogleTranslate, 200);
        }
      }
      setGoogleTranslate();
    });
  });
  document.addEventListener('click', function(e){
    if(!root.contains(e.target)){
      dropdown.style.display = 'none';
    }
  });
});
</script>
<style>
.gst-switcher-custom-menu-item {position:relative;max-width:120px!important;}
.gst-switcher-selected {background:none!important;border:none!important;box-shadow:none!important;padding:6px 10px!important;min-width:90px!important;max-width:120px!important;text-decoration:none!important;color:inherit!important;}
.gst-switcher-selected:focus{box-shadow:none!important;}
.gst-switcher-dropdown {max-width:120px!important;}
.gst-switcher-dropdown .gst-switcher-option {max-width:120px!important;}
.gst-switcher-dropdown .gst-switcher-option:hover{background:#f5f7fa;}
.gst-switcher-dropdown .gst-switcher-option:active{background:#e9ecef;}
@media(max-width:600px){
  .gst-switcher-custom-menu-item{max-width:100%!important;}
  .gst-switcher-selected{font-size:13px!important;padding:5px 7px!important;}
  .gst-switcher-dropdown .gst-switcher-option{font-size:12px!important;padding:5px 7px!important;}
}
</style>
<?php
    } else {
        ?>
        <div id="google_translate_element"></div>
        <script type="text/javascript">
            function googleTranslateElementInit() {
                new google.translate.TranslateElement({
                    pageLanguage: 'en',
                    includedLanguages: '<?php echo esc_js($included); ?>',
                    layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
                    autoDisplay: false
                }, 'google_translate_element');
            }
        </script>
        <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
        <?php
    }
}

// Frontend'de çeviri için gerekli scriptleri ekle
function gst_enqueue_scripts() {
    if (is_front_page() || is_home()) {
        wp_enqueue_script('google-translate', 'https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit', [], null, true);
    }
}
add_action('wp_enqueue_scripts', 'gst_enqueue_scripts');

// Kısa kod ile switcher'ı istediğiniz yere ekleyin
function gst_switcher_shortcode($atts) {
    ob_start();
    gst_render_translate_widget(true);
    return ob_get_clean();
}
add_shortcode('google_site_translator', 'gst_switcher_shortcode');

// Seçilen menüye switcher ekle
function gst_add_switcher_to_menu($items, $args) {
    $selected_menu = get_option('gst_selected_menu', '');
    if (!$selected_menu) return $items;
    $menus = wp_get_nav_menus();
    foreach ($menus as $menu) {
        if ($menu->term_id == $selected_menu && $args->menu->term_id == $selected_menu) {
            $switcher = gst_render_translate_widget(true);
            $items .= '<li class="menu-item gst-switcher-menu">' . $switcher . '</li>';
            break;
        }
    }
    return $items;
}
add_filter('wp_nav_menu_items', 'gst_add_switcher_to_menu', 10, 2);

// Switcher'ı menüde en sağa yaslamak için CSS ekle
add_action('wp_head', function() {
    echo '<style>
    .gst-switcher-menu {float:right !important; margin-left:auto !important; display:flex !important; align-items:center !important;}
    .gst-switcher-custom {margin-right:0 !important;}
    </style>';
});

// Sadece Google'ın orijinal çeviri widget'ı footer'da gösterilecek
add_action('wp_footer', function() {
    echo '<div id="google_translate_element"></div>';
    echo '<script type="text/javascript">
        function googleTranslateElementInit() {
            new google.translate.TranslateElement({
                pageLanguage: "en",
                includedLanguages: "' . esc_js(implode(',', get_option('gst_languages', ['en','de','fr','ru','ar','zh','es','it']))) . '",
                layout: google.translate.TranslateElement.InlineLayout.VERTICAL,
                autoDisplay: false
            }, "google_translate_element");
        }
    </script>';
    echo '<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>';
});
